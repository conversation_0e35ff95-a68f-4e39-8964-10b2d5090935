import time
from concurrent.futures import Thread<PERSON>oolExecutor

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from models.models import CopyrightTask, AuthorizationCode
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from omni.llm.chat.chat_model_factory import ChatModelFactory
from agent.workflow2.workflow_definition import run_workflow1
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod


@register_handler("copyright_task")
class CopyrightTaskApi:

    @auth_required(["user", "admin"])
    async def create(self, data):
        # 获取必要的字段
        user_id = data.get("user_id")  # 用户ID会自动注入
        name = data.get("name")
        description = data.get("description")
        authorization_code = data.get("authorization_code")

        if not description:
            raise MException("任务描述不能为空")

        if not authorization_code:
            raise MException("授权码不能为空")

        # 验证授权码
        auth_code_obj = await AuthorizationCode.find_one(
            AuthorizationCode.code == authorization_code,
            AuthorizationCode.status == "未使用"
        )
        if not auth_code_obj:
            raise MException("授权码无效或已使用")

        # 消费授权码
        auth_code_obj.status = "已使用"
        auth_code_obj.used_at = int(time.time())
        auth_code_obj.user_id = user_id
        await auth_code_obj.save()

        # 创建新任务
        copyright_task = CopyrightTask(
            user_id=user_id,
            name=name,
            description=description,
            status="生成中",
            create_at=int(time.time())
        )
        await copyright_task.insert()
        task_id = str(copyright_task.id)

        # 在后台线程中运行工作流
        # run_workflow1 函数内部有完整的异常处理逻辑，因此这里不需要额外的 try-except
        executor = ThreadPoolExecutor(max_workers=1)
        executor.submit(run_workflow1, description, task_id)
        executor.shutdown(wait=False)

        olog.info(f"已提交软件著作权生成任务, 任务ID: {task_id}")
        return copyright_task.to_dict()

    @auth_required(["user", "admin"])
    async def query_list(self, data):
        user_id = data.get("user_id")  # 用户ID会自动注入
        page = int(data.get("page", 1))
        page_size = int(data.get("page_size", 3))

        # 计算总数
        total = await CopyrightTask.find(CopyrightTask.user_id == user_id).count()

        # 获取分页数据，按创建时间倒序排列
        tasks = await CopyrightTask.find(CopyrightTask.user_id == user_id)\
            .sort(-CopyrightTask.create_at)\
            .skip((page - 1) * page_size)\
            .limit(page_size)\
            .to_list()

        # 转换为字典列表
        tasks_dict = [task.to_dict() for task in tasks]

        # 为已完成且有文档的任务生成下载URL
        oss_client = None
        for task_dict in tasks_dict:
            if task_dict.get("status") == "已完成" and task_dict.get("document_key"):
                try:
                    # 懒加载OSS客户端
                    if oss_client is None:
                        oss_client = OSSClient.getInstance()

                    # 生成临时下载URL，有效期7天
                    download_url = oss_client.signed_url(SignedMethod.GET, task_dict["document_key"])
                    task_dict["download_url"] = download_url
                except Exception as e:
                    olog.exception(f"生成下载链接失败: {str(e)}")
                    # 下载链接生成失败不影响任务信息返回

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": tasks_dict
        }
