import time

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from models.models import CopyrightTask
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod
from omni.msg_queue.redis_set_publisher import publish_messages_to_redis_set
from common_config.common_config import RedisKeyConfig


@register_handler("copyright_task")
class CopyrightTaskApi:

    @auth_required(["user", "admin"])
    async def create(self, data):
        # 获取必要的字段
        user_id = data.get("user_id")  # 用户ID会自动注入
        name = data.get("name")
        description = data.get("description")

        if not description:
            raise MException("任务描述不能为空")

        # 创建新任务
        copyright_task = CopyrightTask(
            user_id=user_id,
            name=name,
            description=description,
            status="生成中",
            create_at=int(time.time())
        )
        await copyright_task.insert()
        task_id = str(copyright_task.id)

        # 发布消息到 Redis 队列
        message = {"task_id": task_id}
        await publish_messages_to_redis_set(RedisKeyConfig.COPYRIGHT_GEN_SET, message)

        olog.info(f"已提交软件著作权生成任务到消息队列, 任务ID: {task_id}")
        return copyright_task.to_dict()

    @auth_required(["user", "admin"])
    async def query_list(self, data):
        user_id = data.get("user_id")  # 用户ID会自动注入
        page = int(data.get("page", 1))
        page_size = int(data.get("page_size", 3))

        # 计算总数
        total = await CopyrightTask.find(CopyrightTask.user_id == user_id).count()

        # 获取分页数据，按创建时间倒序排列
        tasks = await CopyrightTask.find(CopyrightTask.user_id == user_id)\
            .sort(-CopyrightTask.create_at)\
            .skip((page - 1) * page_size)\
            .limit(page_size)\
            .to_list()

        # 转换为字典列表
        tasks_dict = [task.to_dict() for task in tasks]

        # 为已完成且有文档的任务生成下载URL
        oss_client = None
        for task_dict in tasks_dict:
            if task_dict.get("status") == "已完成" and task_dict.get("document_key"):
                try:
                    # 懒加载OSS客户端
                    if oss_client is None:
                        oss_client = OSSClient.getInstance()

                    # 生成临时下载URL，有效期7天
                    download_url = oss_client.signed_url(SignedMethod.GET, task_dict["document_key"])
                    task_dict["download_url"] = download_url
                except Exception as e:
                    olog.exception(f"生成下载链接失败: {str(e)}")
                    # 下载链接生成失败不影响任务信息返回

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": tasks_dict
        }
